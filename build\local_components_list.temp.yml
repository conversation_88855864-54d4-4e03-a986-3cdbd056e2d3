components:
  - name: "app_trace"
    path: "D:/ESP32/v5.2.3/esp-idf/components/app_trace"
  - name: "app_update"
    path: "D:/ESP32/v5.2.3/esp-idf/components/app_update"
  - name: "bootloader"
    path: "D:/ESP32/v5.2.3/esp-idf/components/bootloader"
  - name: "bootloader_support"
    path: "D:/ESP32/v5.2.3/esp-idf/components/bootloader_support"
  - name: "bt"
    path: "D:/ESP32/v5.2.3/esp-idf/components/bt"
  - name: "cmock"
    path: "D:/ESP32/v5.2.3/esp-idf/components/cmock"
  - name: "console"
    path: "D:/ESP32/v5.2.3/esp-idf/components/console"
  - name: "cxx"
    path: "D:/ESP32/v5.2.3/esp-idf/components/cxx"
  - name: "driver"
    path: "D:/ESP32/v5.2.3/esp-idf/components/driver"
  - name: "efuse"
    path: "D:/ESP32/v5.2.3/esp-idf/components/efuse"
  - name: "esp-tls"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp-tls"
  - name: "esp_adc"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_adc"
  - name: "esp_app_format"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_app_format"
  - name: "esp_bootloader_format"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_bootloader_format"
  - name: "esp_coex"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_coex"
  - name: "esp_common"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_common"
  - name: "esp_eth"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_eth"
  - name: "esp_event"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_event"
  - name: "esp_gdbstub"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_gdbstub"
  - name: "esp_hid"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_hid"
  - name: "esp_http_client"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_http_client"
  - name: "esp_http_server"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_http_server"
  - name: "esp_https_ota"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_https_ota"
  - name: "esp_https_server"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_https_server"
  - name: "esp_hw_support"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_hw_support"
  - name: "esp_lcd"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_lcd"
  - name: "esp_local_ctrl"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_local_ctrl"
  - name: "esp_mm"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_mm"
  - name: "esp_netif"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_netif"
  - name: "esp_netif_stack"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_netif_stack"
  - name: "esp_partition"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_partition"
  - name: "esp_phy"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_phy"
  - name: "esp_pm"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_pm"
  - name: "esp_psram"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_psram"
  - name: "esp_ringbuf"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_ringbuf"
  - name: "esp_rom"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_rom"
  - name: "esp_system"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_system"
  - name: "esp_timer"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_timer"
  - name: "esp_wifi"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esp_wifi"
  - name: "espcoredump"
    path: "D:/ESP32/v5.2.3/esp-idf/components/espcoredump"
  - name: "esptool_py"
    path: "D:/ESP32/v5.2.3/esp-idf/components/esptool_py"
  - name: "fatfs"
    path: "D:/ESP32/v5.2.3/esp-idf/components/fatfs"
  - name: "freertos"
    path: "D:/ESP32/v5.2.3/esp-idf/components/freertos"
  - name: "hal"
    path: "D:/ESP32/v5.2.3/esp-idf/components/hal"
  - name: "heap"
    path: "D:/ESP32/v5.2.3/esp-idf/components/heap"
  - name: "http_parser"
    path: "D:/ESP32/v5.2.3/esp-idf/components/http_parser"
  - name: "idf_test"
    path: "D:/ESP32/v5.2.3/esp-idf/components/idf_test"
  - name: "ieee802154"
    path: "D:/ESP32/v5.2.3/esp-idf/components/ieee802154"
  - name: "json"
    path: "D:/ESP32/v5.2.3/esp-idf/components/json"
  - name: "linux"
    path: "D:/ESP32/v5.2.3/esp-idf/components/linux"
  - name: "log"
    path: "D:/ESP32/v5.2.3/esp-idf/components/log"
  - name: "lwip"
    path: "D:/ESP32/v5.2.3/esp-idf/components/lwip"
  - name: "mbedtls"
    path: "D:/ESP32/v5.2.3/esp-idf/components/mbedtls"
  - name: "mqtt"
    path: "D:/ESP32/v5.2.3/esp-idf/components/mqtt"
  - name: "newlib"
    path: "D:/ESP32/v5.2.3/esp-idf/components/newlib"
  - name: "nvs_flash"
    path: "D:/ESP32/v5.2.3/esp-idf/components/nvs_flash"
  - name: "nvs_sec_provider"
    path: "D:/ESP32/v5.2.3/esp-idf/components/nvs_sec_provider"
  - name: "openthread"
    path: "D:/ESP32/v5.2.3/esp-idf/components/openthread"
  - name: "partition_table"
    path: "D:/ESP32/v5.2.3/esp-idf/components/partition_table"
  - name: "perfmon"
    path: "D:/ESP32/v5.2.3/esp-idf/components/perfmon"
  - name: "protobuf-c"
    path: "D:/ESP32/v5.2.3/esp-idf/components/protobuf-c"
  - name: "protocomm"
    path: "D:/ESP32/v5.2.3/esp-idf/components/protocomm"
  - name: "pthread"
    path: "D:/ESP32/v5.2.3/esp-idf/components/pthread"
  - name: "riscv"
    path: "D:/ESP32/v5.2.3/esp-idf/components/riscv"
  - name: "sdmmc"
    path: "D:/ESP32/v5.2.3/esp-idf/components/sdmmc"
  - name: "soc"
    path: "D:/ESP32/v5.2.3/esp-idf/components/soc"
  - name: "spi_flash"
    path: "D:/ESP32/v5.2.3/esp-idf/components/spi_flash"
  - name: "spiffs"
    path: "D:/ESP32/v5.2.3/esp-idf/components/spiffs"
  - name: "tcp_transport"
    path: "D:/ESP32/v5.2.3/esp-idf/components/tcp_transport"
  - name: "touch_element"
    path: "D:/ESP32/v5.2.3/esp-idf/components/touch_element"
  - name: "ulp"
    path: "D:/ESP32/v5.2.3/esp-idf/components/ulp"
  - name: "unity"
    path: "D:/ESP32/v5.2.3/esp-idf/components/unity"
  - name: "usb"
    path: "D:/ESP32/v5.2.3/esp-idf/components/usb"
  - name: "vfs"
    path: "D:/ESP32/v5.2.3/esp-idf/components/vfs"
  - name: "wear_levelling"
    path: "D:/ESP32/v5.2.3/esp-idf/components/wear_levelling"
  - name: "wifi_provisioning"
    path: "D:/ESP32/v5.2.3/esp-idf/components/wifi_provisioning"
  - name: "wpa_supplicant"
    path: "D:/ESP32/v5.2.3/esp-idf/components/wpa_supplicant"
  - name: "xtensa"
    path: "D:/ESP32/v5.2.3/esp-idf/components/xtensa"
  - name: "main"
    path: "D:/ESP32/project/handheld/main"
