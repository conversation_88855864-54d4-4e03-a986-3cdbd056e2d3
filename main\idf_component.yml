## IDF Component Manager Manifest File
dependencies:
  espressif/esp32-camera: "^2.0.10"         # 摄像头驱动
  lvgl/lvgl: "~8.3.0"
  espressif/esp_lvgl_port: "~1.4.0"         # LVGL接口
  espressif/esp_lcd_touch_ft5x06: "~1.0.6"  # 触摸屏驱动
  chmorgan/esp-audio-player: "~1.0.7"       # 音频播放
  chmorgan/esp-file-iterator: "1.0.0"       # 获取文件
  espressif/esp_codec_dev: "~1.3.0"         # 音频驱动
  espressif/esp-sr: "~1.6.0"                # 语音识别
  ## Required IDF version
  idf:
    version: ">=4.1.0"
  # # Put list of dependencies here
  # # For components maintained by Espressif:
  # component: "~1.0.0"
  # # For 3rd party components:
  # username/component: ">=1.0.0,<2.0.0"
  # username2/component2:
  #   version: "~1.0.0"
  #   # For transient dependencies `public` flag can be set.
  #   # `public` flag doesn't have an effect dependencies of the `main` component.
  #   # All dependencies of `main` are public by default.
  #   public: true
