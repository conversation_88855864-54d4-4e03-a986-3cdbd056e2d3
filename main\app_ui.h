#pragma once


/*********************** 开机界面 ****************************/
// 开机界面
void lv_gui_start(void);


/*********************** 主界面 ****************************/
void lv_main_page(void);


/*********************** 音乐播放器 ****************************/
void mp3_player_init(void);
void music_ui(void);

void ai_gui_in(void);
void ai_gui_out(void);

void ai_play(void);
void ai_pause(void);
void ai_resume(void);
void ai_prev_music(void);
void ai_next_music(void);
void ai_volume_up(void);
void ai_volume_down(void);



void ai_open_icon1(void);
void ai_open_icon2(void);
void ai_open_icon3(void);
void ai_open_icon4(void);
void ai_open_icon5(void);
void ai_open_icon6(void);
void ai_tuichu(void);

